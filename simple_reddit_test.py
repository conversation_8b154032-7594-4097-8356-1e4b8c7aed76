#!/usr/bin/env python3
"""
Simple Reddit test - works without credentials using public Reddit API
This script tests basic Reddit connectivity and post retrieval
Extended to send posts to Discord via localhost:5000
"""
import requests
import json
from datetime import datetime
from typing import List, Dict, Any
from config import Config
import time
import praw
from dotenv import load_dotenv
import os
import threading
from flask import Flask

def test_public_reddit_api() -> bool:
    """Test Reddit's public API without authentication"""
    print("🔗 Testing Public Reddit API...")
    
    try:
        # Test basic Reddit connectivity
        url = "https://www.reddit.com/r/test.json"
        headers = {'User-Agent': 'RedditTestBot/1.0'}
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        if 'data' in data and 'children' in data['data']:
            print("✅ Reddit API is accessible")
            return True
        else:
            print("❌ Unexpected Reddit API response format")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Reddit API connection failed: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON response from Reddit: {e}")
        return False

def fetch_posts_from_all_reddit(limit: int = 25) -> List[Dict[str, Any]]:
    """Fetch new posts from all Reddit subreddits using PRAW (Reddit API)"""
    print(f"📰 Fetching {limit} newest posts from all Reddit using PRAW...")

    # Ensure environment variables are loaded
    load_dotenv()

    reddit = praw.Reddit(
        client_id=Config.REDDIT_CLIENT_ID,
        client_secret=Config.REDDIT_CLIENT_SECRET,
        user_agent=Config.REDDIT_USER_AGENT
    )

    posts = []
    try:
        for submission in reddit.subreddit('all').new(limit=limit):
            post = {
                'id': submission.id,
                'title': submission.title,
                'subreddit': str(submission.subreddit),
                'author': str(submission.author) if submission.author else '[deleted]',
                'score': submission.score,
                'num_comments': submission.num_comments,
                'created_utc': int(submission.created_utc),
                'permalink': submission.permalink,
                'url': f"https://reddit.com{submission.permalink}",
                'selftext': submission.selftext or ''
            }
            posts.append(post)
        print(f"   ✅ Successfully fetched {len(posts)} posts from all Reddit (PRAW)")
        return posts
    except Exception as e:
        print(f"   ❌ Error fetching from Reddit with PRAW: {e}")
        return []

def fetch_posts_from_subreddit(subreddit: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Fetch posts from a specific subreddit using public API (kept for backward compatibility)"""
    print(f"📰 Fetching posts from r/{subreddit}...")

    try:
        url = f"https://www.reddit.com/r/{subreddit}/new.json?limit={limit}"
        headers = {'User-Agent': 'RedditTestBot/1.0'}

        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()

        data = response.json()
        posts = []

        if 'data' in data and 'children' in data['data']:
            for child in data['data']['children']:
                post_data = child['data']

                # Extract relevant post information
                post = {
                    'id': post_data.get('id', ''),
                    'title': post_data.get('title', ''),
                    'subreddit': post_data.get('subreddit', ''),
                    'author': post_data.get('author', '[deleted]'),
                    'score': post_data.get('score', 0),
                    'num_comments': post_data.get('num_comments', 0),
                    'created_utc': post_data.get('created_utc', 0),
                    'permalink': post_data.get('permalink', ''),
                    'url': f"https://reddit.com{post_data.get('permalink', '')}",
                    'selftext': post_data.get('selftext', '')
                }
                posts.append(post)

            print(f"   ✅ Successfully fetched {len(posts)} posts from r/{subreddit}")
            return posts
        else:
            print(f"   ❌ No posts found in r/{subreddit}")
            return []

    except requests.exceptions.RequestException as e:
        print(f"   ❌ Error fetching from r/{subreddit}: {e}")
        return []
    except json.JSONDecodeError as e:
        print(f"   ❌ Invalid JSON from r/{subreddit}: {e}")
        return []

def load_queries_from_file(filename: str = 'queries.txt') -> list:
    """Load and parse queries from the queries.txt file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                print("❌ queries.txt is empty")
                return []
            queries = []
            for line in content.split('\n'):
                if line.strip():
                    line_queries = [q.strip().strip('"\'').lower() for q in line.split(' OR ') if q.strip()]
                    queries.extend(line_queries)
            print(f"   📝 Loaded {len(queries)} search terms from {filename}")
            return queries
    except Exception as e:
        print(f"❌ Error loading queries: {e}")
        return []

def post_matches_queries(post: Dict[str, Any], queries: list) -> str:
    """Return the exact query from queries.txt that matches as a substring in the post's title or selftext (case-insensitive, ignoring quotes)."""
    title_lower = post['title'].lower()
    content_lower = post['selftext'].lower()

    for query in queries:
        # Remove quotes and extra spaces from query for matching, but return the original query
        query_clean = query.strip().strip('"\'').lower()
        if not query_clean:
            continue
        if query_clean in title_lower or query_clean in content_lower:
            print(f"   🎯 Match found for query '{query}' in post: {post['title'][:50]}...")
            return query.strip()  # Return the original query as in queries.txt
    return ""

def send_post_to_discord(post: Dict[str, Any], matched_query: str = "") -> bool:
    """Send a Reddit post to Discord using the webhook URL from config, always mentioning the matched query if provided."""
    webhook_url = getattr(Config, 'DISCORD_WEBHOOK_URL', None)
    if not webhook_url:
        print("❌ Discord webhook URL not configured in Config.DISCORD_WEBHOOK_URL")
        return False
    print(f"📤 Sending post to Discord via webhook: {webhook_url[:60]}...")

    try:
        created_time = datetime.fromtimestamp(post['created_utc']).strftime('%Y-%m-%d %H:%M:%S')
        summary = f"**🔥 New Reddit Match Found!**\n\n"
        summary += f"**Title:** {post['title'][:200]}\n"
        if matched_query:
            summary += f"**Matched Query:** `{matched_query}`\n"
        summary += f"**URL:** {post['url']}\n"
        summary += f"**Author:** u/{post['author']}\n"
        summary += f"**Stats:** 👍 {post['score']} | 💬 {post['num_comments']} | 🕒 {created_time}\n"
        if post['selftext']:
            content_preview = post['selftext'][:300]
            if len(post['selftext']) > 300:
                content_preview += "..."
            summary += f"\n**Content:**\n{content_preview}"

        discord_payload = {
            "content": summary,
            "embeds": [
                {
                    "title": post['title'][:256],
                    "url": post['url'],
                    "color": 0xFF4500,
                    "timestamp": datetime.fromtimestamp(post['created_utc']).isoformat() + "Z",
                    "footer": {
                        "text": f"r/{post['subreddit']} • Reddit Auto Monitor",
                        "icon_url": "https://www.redditstatic.com/desktop2x/img/favicon/favicon-32x32.png"
                    },
                    "author": {
                        "name": f"u/{post['author']}",
                        "url": f"https://reddit.com/u/{post['author']}"
                    },
                    "fields": [
                        {
                            "name": "🔍 Matched Query",
                            "value": f"`{matched_query}`" if matched_query else "No query matched.",
                            "inline": False
                        },
                        {
                            "name": "📊 Stats",
                            "value": f"👍 {post['score']} • 💬 {post['num_comments']}",
                            "inline": True
                        },
                        {
                            "name": "🕒 Posted",
                            "value": created_time,
                            "inline": True
                        },
                        {
                            "name": "🌐 Subreddit",
                            "value": f"r/{post['subreddit']}",
                            "inline": True
                        }
                    ]
                }
            ]
        }
        if post['selftext']:
            content_preview = post['selftext'][:1000]
            if len(post['selftext']) > 1000:
                content_preview += "..."
            discord_payload["embeds"][0]["fields"].append({
                "name": "📝 Post Content",
                "value": content_preview,
                "inline": False
            })

        headers = {'Content-Type': 'application/json'}
        response = requests.post(webhook_url, json=discord_payload, headers=headers, timeout=10)
        response.raise_for_status()

        print(f"   ✅ Successfully sent post to Discord: {post['title'][:50]}...")
        return True

    except requests.exceptions.RequestException as e:
        print(f"   ❌ Failed to send to Discord: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error sending to Discord: {e}")
        return False

def reddit_to_discord_bot():
    """Main bot loop: fetch posts from all Reddit, match queries, send to Discord"""
    print("🤖 Starting Reddit-to-Discord Real-time Monitor...")
    print("🌐 Monitoring ALL Reddit subreddits for keyword matches")

    # Load search queries from file
    queries = load_queries_from_file('queries.txt')
    if not queries:
        print("❌ No queries loaded. Exiting.")
        return

    print(f"🔍 Loaded {len(queries)} search terms")
    print(f"📝 Search terms: {', '.join(queries[:5])}{'...' if len(queries) > 5 else ''}")

    sent_post_ids = set()
    fetch_limit = 50  # Increased limit for better coverage
    poll_interval = 30  # Reduced interval for more real-time monitoring

    print(f"⚙️  Configuration:")
    print(f"   📊 Fetch limit: {fetch_limit} posts per check")
    print(f"   ⏱️  Poll interval: {poll_interval} seconds")
    print(f"🔁 Starting continuous monitoring...")

    while True:
        try:
            # Fetch new posts from all Reddit
            posts = fetch_posts_from_all_reddit(fetch_limit)

            matches_found = 0
            for post in posts:
                # Skip if we've already processed this post
                if post['id'] in sent_post_ids:
                    continue

                # Check if post matches any of our queries
                matched_query = post_matches_queries(post, queries)
                if matched_query:
                    matches_found += 1
                    print(f"🎯 Match #{matches_found} found in r/{post['subreddit']}: {post['title'][:60]}...")
                    success = send_post_to_discord(post, matched_query)
                    if success:
                        sent_post_ids.add(post['id'])
                        print(f"   ✅ Notification sent successfully")
                    else:
                        print(f"   ❌ Failed to send notification")

            if matches_found == 0:
                print(f"   ℹ️  No matches found in {len(posts)} posts checked")
            else:
                print(f"   🎉 Found {matches_found} matching posts this round")

            # Clean up old post IDs to prevent memory buildup (keep last 1000)
            if len(sent_post_ids) > 1000:
                sent_post_ids = set(list(sent_post_ids)[-1000:])

            print(f"⏳ Sleeping for {poll_interval} seconds...")
            time.sleep(poll_interval)

        except KeyboardInterrupt:
            print("\n⏹️  Bot interrupted by user")
            break
        except Exception as e:
            print(f"❌ Error in main loop: {e}")
            print("🔄 Continuing after 30 seconds...")
            time.sleep(30)

def start_dummy_server():
    app = Flask(__name__)

    @app.route("/")
    def index():
        return "✅ Reddit Bot is running."

    port = int(os.environ.get("PORT", 5000))
    app.run(host="0.0.0.0", port=port)

def reddit_subreddits_to_discord_bot():
    """
    Monitor specific subreddits and send new posts to a separate Discord webhook.
    Uses SUBREDDITS and DISCORD_WEBHOOK_SUBREDDITS from .env/config.
    """
    print("🤖 Starting Subreddit-specific Reddit-to-Discord Monitor...")

    # Load subreddits from environment/config
    subreddits_str = os.environ.get("SUBREDDITS") or getattr(Config, "SUBREDDITS", "")
    webhook_url = os.environ.get("DISCORD_WEBHOOK_SUBREDDITS") or getattr(Config, "DISCORD_WEBHOOK_SUBREDDITS", None)

    if not subreddits_str:
        print("❌ No subreddits specified in SUBREDDITS env/config.")
        return
    if not webhook_url:
        print("❌ No Discord webhook specified in DISCORD_WEBHOOK_SUBREDDITS.")
        return

    subreddits = [s.strip() for s in subreddits_str.split(",") if s.strip()]
    print(f"🌐 Monitoring subreddits: {', '.join(subreddits)}")
    print(f"🔗 Using Discord webhook: {webhook_url[:60]}...")

    sent_post_ids = set()
    fetch_limit = 20
    poll_interval = 60

    while True:
        try:
            for subreddit in subreddits:
                posts = fetch_posts_from_subreddit(subreddit, limit=fetch_limit)
                for post in posts:
                    if post['id'] in sent_post_ids:
                        continue
                    # Send every new post (no keyword filtering)
                    success = send_post_to_discord_subreddit(post, webhook_url)
                    if success:
                        sent_post_ids.add(post['id'])
                        print(f"   ✅ Sent post from r/{subreddit}: {post['title'][:50]}...")
            # Clean up old post IDs
            if len(sent_post_ids) > 1000:
                sent_post_ids = set(list(sent_post_ids)[-1000:])
            print(f"⏳ Sleeping for {poll_interval} seconds (subreddit monitor)...")
            time.sleep(poll_interval)
        except KeyboardInterrupt:
            print("\n⏹️  Subreddit bot interrupted by user")
            break
        except Exception as e:
            print(f"❌ Error in subreddit monitor: {e}")
            time.sleep(30)

def send_post_to_discord_subreddit(post: Dict[str, Any], webhook_url: str) -> bool:
    """Send a Reddit post to a specific Discord webhook (for subreddits)."""
    try:
        created_time = datetime.fromtimestamp(post['created_utc']).strftime('%Y-%m-%d %H:%M:%S')
        summary = f"**🆕 New post in r/{post['subreddit']}**\n\n"
        summary += f"**Title:** {post['title'][:200]}\n"
        summary += f"**URL:** {post['url']}\n"
        summary += f"**Author:** u/{post['author']}\n"
        summary += f"**Stats:** 👍 {post['score']} | 💬 {post['num_comments']} | 🕒 {created_time}\n"
        if post['selftext']:
            content_preview = post['selftext'][:300]
            if len(post['selftext']) > 300:
                content_preview += "..."
            summary += f"\n**Content:**\n{content_preview}"

        discord_payload = {
            "content": summary,
            "embeds": [
                {
                    "title": post['title'][:256],
                    "url": post['url'],
                    "color": 0x5A9EC9,  # Blue for subreddit monitor
                    "timestamp": datetime.fromtimestamp(post['created_utc']).isoformat() + "Z",
                    "footer": {
                        "text": f"r/{post['subreddit']} • Subreddit Monitor",
                        "icon_url": "https://www.redditstatic.com/desktop2x/img/favicon/favicon-32x32.png"
                    },
                    "author": {
                        "name": f"u/{post['author']}",
                        "url": f"https://reddit.com/u/{post['author']}"
                    },
                    "fields": [
                        {
                            "name": "📊 Stats",
                            "value": f"👍 {post['score']} • 💬 {post['num_comments']}",
                            "inline": True
                        },
                        {
                            "name": "🕒 Posted",
                            "value": created_time,
                            "inline": True
                        }
                    ]
                }
            ]
        }
        if post['selftext']:
            content_preview = post['selftext'][:1000]
            if len(post['selftext']) > 1000:
                content_preview += "..."
            discord_payload["embeds"][0]["fields"].append({
                "name": "📝 Post Content",
                "value": content_preview,
                "inline": False
            })

        headers = {'Content-Type': 'application/json'}
        response = requests.post(webhook_url, json=discord_payload, headers=headers, timeout=10)
        response.raise_for_status()

        print(f"   ✅ Successfully sent post to subreddit Discord: {post['title'][:50]}...")
        return True

    except requests.exceptions.RequestException as e:
        print(f"   ❌ Failed to send to subreddit Discord: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error sending to subreddit Discord: {e}")
        return False

# 🔥 Start dummy server in background so Render sees a port is open
threading.Thread(target=start_dummy_server).start()
# 🔥 Start subreddit monitor in background
threading.Thread(target=reddit_subreddits_to_discord_bot, daemon=True).start()

# Run the main Reddit to Discord bot loop
reddit_to_discord_bot()
